package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;
import com.dell.it.hip.util.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.routing.RoutingDecision;
import com.dell.it.hip.util.routing.RoutingRuleEngine;

@Component("dynamicRouter")
public class DynamicRoutingFlowStep extends AbstractFlowStepStrategy {

    private static final Logger logger = LoggerFactory.getLogger(DynamicRoutingFlowStep.class);

    @Autowired
    private RoutingRuleEngine ruleEngine;

    @Autowired
    private WiretapService wiretapService;

    @Autowired
    private TransactionLoggingUtil transactionLoggingUtil;

    @Autowired
    private ServiceManager serviceManager;

    @Override
    public String getType() {
        return "dynamicRouter";
    }

    @Override
    protected List<Message<?>> doExecute(Message<?> message, FlowStepConfigRef ref, HIPIntegrationDefinition def) throws Exception {
        try {
            // Get the actual FlowStepConfig from the ref
            FlowStepConfig stepConfig = def.getConfig(ref.getPropertyRef(), FlowStepConfig.class);
            if (stepConfig == null) {
                logger.warn("No FlowStepConfig found for propertyRef: {}", ref.getPropertyRef());
                return Collections.emptyList();
            }

            // Extract routing key/criteria
            String ruleKey = stepConfig.getParameters() != null
                    ? (String) stepConfig.getParameters().get("ruleKey")
                    : null;
            if (ruleKey == null) {
                logger.warn("No routing ruleKey specified in FlowStepConfig parameters.");
                return Collections.emptyList();
            }

            // Evaluate routing decision
            RoutingDecision decision = ruleEngine.evaluate(ruleKey, message, stepConfig, def);

            if (decision == null) {
                logger.warn("No routing decision for ruleKey={}", ruleKey);
                return Collections.emptyList();
            }

            if (decision.getRouteType() == RoutingDecision.RouteType.CHANNEL) {
                String channelName = decision.getChannelName();
                MessageChannel target = getMessageChannelByName(channelName, def);
                if (target != null) {
                    target.send(message);
                    return Collections.emptyList();
                } else {
                    logger.error("Target channel not found: {}", channelName);
                    return Collections.emptyList();
                }
            }

            if (decision.getRouteType() == RoutingDecision.RouteType.HANDLER) {
                HandlerConfigRef handlerRef = decision.getHandlerConfigRef();
                if (handlerRef == null) {
                    logger.error("RoutingDecision indicated HANDLER, but HandlerConfigRef is null.");
                    return Collections.emptyList();
                }
                HandlerStrategy strategy = serviceManager.getHandlerStrategy(handlerRef.getType());
                if (strategy != null) {
                    strategy.handle(message, def, handlerRef);
                    return Collections.emptyList();
                } else {
                    logger.error("HandlerStrategy not found for type: {}", handlerRef.getType());
                    return Collections.emptyList();
                }
            }

            // No match
            return Collections.emptyList();

        } catch (Exception ex) {
            logger.error("Exception in dynamic routing step: {}", ex.getMessage(), ex);
            throw ex;
        }
    }

    /**
     * Helper method to get a message channel by name from the integration's topology
     */
    private MessageChannel getMessageChannelByName(String channelName, HIPIntegrationDefinition def) {
        // Try to get the channel from the service manager's topology
        List<MessageChannel> channels = serviceManager.getChannels(def.getHipIntegrationName(), def.getVersion());

        // For now, we'll use a simple approach - you might need to enhance this based on your channel naming strategy
        if (channels != null && !channels.isEmpty()) {
            // Return the first channel as a fallback - this should be enhanced with proper channel lookup
            return channels.get(0);
        }

        return null;
    }
}