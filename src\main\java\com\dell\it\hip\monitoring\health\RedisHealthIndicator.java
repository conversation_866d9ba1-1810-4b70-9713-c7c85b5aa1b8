package com.dell.it.hip.monitoring.health;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * Health indicator for Redis connectivity.
 */
@Component
public class RedisHealthIndicator implements HealthIndicator {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public Health health() {
        try {
            // Test Redis connectivity with a simple ping
            String result = redisTemplate.getConnectionFactory()
                    .getConnection()
                    .ping();
            
            if ("PONG".equals(result)) {
                return Health.up()
                        .withDetail("redis", "Available")
                        .withDetail("response", result)
                        .build();
            } else {
                return Health.down()
                        .withDetail("redis", "Unexpected response")
                        .withDetail("response", result)
                        .build();
            }
        } catch (Exception ex) {
            return Health.down()
                    .withDetail("redis", "Connection failed")
                    .withDetail("error", ex.getMessage())
                    .build();
        }
    }
}
