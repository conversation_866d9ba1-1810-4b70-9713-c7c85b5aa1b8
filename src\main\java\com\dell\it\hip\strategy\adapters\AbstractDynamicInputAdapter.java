package com.dell.it.hip.strategy.adapters;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.context.NamedComponent;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfig;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.adapters.DynamicKafkaAdapterConfig;
import com.dell.it.hip.core.HIPClusterCoordinationService;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;

/**
 * Abstract base class for all dynamic input adapters in HIP.
 * Handles cluster coordination, OTel context propagation, and transaction logging.
 */


public abstract class AbstractDynamicInputAdapter implements InputAdapterStrategy {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired protected HIPClusterCoordinationService clusterCoordinationService;
    @Autowired protected TransactionLoggingUtil transactionLoggingUtil;
    @Autowired protected OpenTelemetryPropagationUtil openTelemetryPropagationUtil;
    @Autowired protected HIPIntegrationOrchestrationService hipIntegrationOrchestrationService;
    @Autowired protected WiretapService wiretapService;
    // integrationName:version:adapterId -> AdapterInstance
    protected final Map<String, AdapterInstance> adapterInstances = new ConcurrentHashMap<>();

    @Override
    public void buildProducers(HIPIntegrationDefinition def) {
        for (AdapterConfigRef ref : def.getAdapterConfigRefs()) {
            buildProducer(def, ref);
        }
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = key(def, ref);
        AdapterInstance instance = adapterInstances.remove(key);
        if (instance != null) {
            shutdownAdapterInstance(def, ref, instance);
        }
        clusterCoordinationService.resume(def, ref); // ensure resumed in cluster state
        clusterCoordinationService.removeThrottle(def, ref); // remove throttling
        logger.info("Adapter shutdown: {}", key);
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def) {
        for (AdapterConfigRef ref : def.getAdapterConfigRefs()) {
            shutdown(def, ref);
        }
    }

    @Override
    public void pause(HIPIntegrationDefinition def) {
        for (AdapterConfigRef ref : def.getAdapterConfigRefs()) {
            pause(def, ref);
        }
    }

    @Override
    public void pause(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = key(def, ref);
        AdapterInstance instance = adapterInstances.get(key);
        if (instance != null) {
            doPause(def, ref, instance);
            clusterCoordinationService.pause(def, ref);
            logger.info("Adapter paused: {}", key);
        }
    }

    @Override
    public void resume(HIPIntegrationDefinition def) {
        for (AdapterConfigRef ref : def.getAdapterConfigRefs()) {
            resume(def, ref);
        }
    }

    @Override
    public void resume(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = key(def, ref);
        AdapterInstance instance = adapterInstances.get(key);
        if (instance != null) {
            doResume(def, ref, instance);
            clusterCoordinationService.resume(def, ref);
            logger.info("Adapter resumed: {}", key);
        }
    }

    @Override
    public void updateThrottle(HIPIntegrationDefinition def, ThrottleSettings throttleSettings) {
        for (AdapterConfigRef ref : def.getAdapterConfigRefs()) {
            updateThrottle(def, ref, throttleSettings);
        }
    }

    @Override
    public void updateThrottle(HIPIntegrationDefinition def, AdapterConfigRef ref, ThrottleSettings throttleSettings) {
        clusterCoordinationService.setThrottle(def, ref, throttleSettings);
        logger.info("Throttle updated: {} - {}", key(def, ref), throttleSettings);
    }

    @Override
    public abstract String getType();

    @Override
    public void startAll() {
        adapterInstances.values().forEach(inst -> {
            try { startAdapterInstance(inst); } catch (Exception ex) { logger.warn("Failed to start instance: {}", ex.getMessage()); }
        });
    }

    @Override
    public void stopAll() {
        adapterInstances.values().forEach(inst -> {
            try { stopAdapterInstance(inst); } catch (Exception ex) { logger.warn("Failed to stop instance: {}", ex.getMessage()); }
        });
    }

    protected void processInboundMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw, MessageChannel inputChannel) {
        try {
            ThrottleSettings settings = getThrottleSettings(def, ref);
            if (!clusterCoordinationService.isInputAllowed(def, ref, settings)) {
                logger.warn("Rate limit exceeded for adapter {}", key(def, ref));
                transactionLoggingUtil.sendErrorEvent(def, null, new RuntimeException("Rate limit exceeded"));
                return;
            }

            if (isDuplicate(def, ref, raw)) {
                logger.warn("Duplicate message for adapter {}", key(def, ref));
                transactionLoggingUtil.sendErrorEvent(def, null, new RuntimeException("Duplicate message"));
                return;
            }

            Message<?> msg = toMessage(def, ref, raw);
            Message<?> msgWithTrace = openTelemetryPropagationUtil.injectTraceContext(msg);
            transactionLoggingUtil.sendStarted(msgWithTrace, def, "INBOUND");
            inputChannel.send(msgWithTrace);

        } catch (Exception ex) {
            transactionLoggingUtil.sendErrorEvent(def, null, ex);
            logger.error("Failed to process inbound message for adapter {}: {}", key(def, ref), ex.getMessage(), ex);
        }
    }

    protected String key(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        return def.getHipIntegrationName() + ":" + def.getVersion() + ":" + ref.getId();
    }

    protected void registerAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        adapterInstances.put(key(def, ref), instance);
    }

    // --- Header Promotion Utility ---

    /**
     * Promotes headers from the adapter's raw message into the Spring message.
     * Adapter-specific headers are grouped under "HIP.adapter.<adapterType>.*".
     * Generic headers (traceparent, etc) are promoted top-level.
     */
    protected void promoteHeaders(
            Map<String, ?> sourceHeaders,
            MessageBuilder<?> builder,
            HIPIntegrationDefinition def,
            AdapterConfigRef ref
    ) {
        List<String> genericHeaders = getGenericHeadersToPromote(def, ref);
        String adapterPrefix = "HIP.adapter." + ref.getType() + ".";
        List<String> headersToExtract = getHeadersToExtract(def, ref);

        for (Map.Entry<String, ?> entry : sourceHeaders.entrySet()) {
            String key = entry.getKey();
            Object val = entry.getValue();
            if (val == null) continue;
            if (headersToExtract != null && !headersToExtract.contains(key)) continue; // skip not listed
            if (genericHeaders.contains(key)) {
                builder.setHeader(key, val);
            } else {
                builder.setHeader(adapterPrefix + key, val);
            }
        }
    }

    /**
     * Retrieves the list of headers to extract from the property sheet or config.
     * Defaults to all if not set (or could return a platform default).
     */
    protected List<String> getHeadersToExtract(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        AdapterConfig cfg = (AdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg instanceof DynamicKafkaAdapterConfig kafkaCfg && kafkaCfg.getHeadersToExtract() != null) {
            return kafkaCfg.getHeadersToExtract();
        }
        // For other adapters/configs, extend as needed.
        // if (def.getHeadersToExtract() != null) return def.getHeadersToExtract();
        return null; // null = allow all (or restrict as desired)
    }

    /** Returns the generic/canonical headers to always promote, configurable if you wish. */
    protected List<String> getGenericHeadersToPromote(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        // Add/remove as needed for your standard flow
        return List.of("traceparent", "tracestate", "businessKey", "correlationId");
    }

    // --- Abstract/Overridable Methods for Concrete Adapters ---

    @Override
    public abstract void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref);
    protected abstract Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw);
    protected abstract void shutdownAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance);
    protected abstract void startAdapterInstance(AdapterInstance instance);
    protected abstract void stopAdapterInstance(AdapterInstance instance);
    protected abstract void doPause(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance);
    protected abstract void doResume(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance);

    protected boolean isDuplicate(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw) { return false; }
    protected ThrottleSettings getThrottleSettings(HIPIntegrationDefinition def, AdapterConfigRef ref) { return null; }
    protected MessageChannel getInputChannel(HIPIntegrationDefinition def){

        return hipIntegrationOrchestrationService.getInputChannel( def.getHipIntegrationName(),def.getVersion());
    }
    protected String getChannelName(MessageChannel channel) {
        if (channel instanceof NamedComponent named) {
            return named.getComponentName();
        }
        return null; // Or throw, or handle as appropriate
    }

    public static class AdapterInstance {
        // Each implementation can extend this for its own runtime fields
    }
}