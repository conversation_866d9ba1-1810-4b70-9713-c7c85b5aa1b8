package com.dell.it.hip.util.routing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.core.registry.HIPIntegrationRegistry;

/**
 * Unit tests for RoutingRuleEngine.
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class RoutingRuleEngineTest {

    @Mock
    private HIPIntegrationRegistry hipIntegrationRegistry;

    @InjectMocks
    private RoutingRuleEngine routingRuleEngine;

    private Message<?> testMessage;
    private FlowStepConfig stepConfig;
    private HIPIntegrationDefinition integrationDefinition;
    private List<RoutingRule> routingRules;

    @BeforeEach
    void setUp() {
        testMessage = new GenericMessage<>("test payload");
        stepConfig = new FlowStepConfig();
        
        integrationDefinition = new HIPIntegrationDefinition();
        integrationDefinition.setHipIntegrationName("test-integration");
        integrationDefinition.setVersion("1.0");
        
        // Create test handler config refs
        HandlerConfigRef handlerRef1 = new HandlerConfigRef();
        handlerRef1.setId("handler-1");
        handlerRef1.setType("http-handler");
        
        HandlerConfigRef handlerRef2 = new HandlerConfigRef();
        handlerRef2.setId("handler-2");
        handlerRef2.setType("kafka-handler");
        
        integrationDefinition.setHandlerConfigRefs(Arrays.asList(handlerRef1, handlerRef2));
        
        // Create test routing rules
        RoutingRule channelRule = new RoutingRule();
        channelRule.setRouteType(RoutingDecision.RouteType.CHANNEL);
        channelRule.setChannelName("test-channel");
        channelRule.setCondition("true");
        
        RoutingRule handlerRule = new RoutingRule();
        handlerRule.setRouteType(RoutingDecision.RouteType.HANDLER);
        handlerRule.setHandlerConfigRefId("handler-1");
        handlerRule.setCondition("true");
        
        routingRules = Arrays.asList(channelRule, handlerRule);
    }

    @Test
    void testEvaluate_ChannelRouting_Success() {
        // Arrange
        String ruleKey = "test-rule";
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenReturn(routingRules);

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, testMessage, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.CHANNEL, result.getRouteType());
        assertEquals("test-channel", result.getChannelName());
        verify(hipIntegrationRegistry).findRoutingRulesByKey(ruleKey);
    }

    @Test
    void testEvaluate_HandlerRouting_Success() {
        // Arrange
        String ruleKey = "test-rule";
        // Return only the handler rule
        RoutingRule handlerRule = routingRules.get(1);
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenReturn(Collections.singletonList(handlerRule));

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, testMessage, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.HANDLER, result.getRouteType());
        assertNotNull(result.getHandlerConfigRef());
        assertEquals("handler-1", result.getHandlerConfigRef().getId());
        assertEquals("http-handler", result.getHandlerConfigRef().getType());
    }

    @Test
    void testEvaluate_NoRulesFound_ReturnsNone() {
        // Arrange
        String ruleKey = "non-existent-rule";
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenReturn(Collections.emptyList());

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, testMessage, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.NONE, result.getRouteType());
    }

    @Test
    void testEvaluate_HandlerNotFound_ReturnsNone() {
        // Arrange
        String ruleKey = "test-rule";
        RoutingRule handlerRule = new RoutingRule();
        handlerRule.setRouteType(RoutingDecision.RouteType.HANDLER);
        handlerRule.setHandlerConfigRefId("non-existent-handler");
        handlerRule.setCondition("true");
        
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenReturn(Collections.singletonList(handlerRule));

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, testMessage, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.NONE, result.getRouteType());
    }

    @Test
    void testEvaluate_NullRuleKey_ReturnsNone() {
        // Act
        RoutingDecision result = routingRuleEngine.evaluate(null, testMessage, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.NONE, result.getRouteType());
        verify(hipIntegrationRegistry, never()).findRoutingRulesByKey(any());
    }

    @Test
    void testEvaluate_EmptyRuleKey_ReturnsNone() {
        // Act
        RoutingDecision result = routingRuleEngine.evaluate("", testMessage, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.NONE, result.getRouteType());
        verify(hipIntegrationRegistry, never()).findRoutingRulesByKey(any());
    }

    @Test
    void testEvaluate_NullMessage_ReturnsNone() {
        // Arrange
        String ruleKey = "test-rule";
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenReturn(routingRules);

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, null, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.NONE, result.getRouteType());
    }

    @Test
    void testEvaluate_NullIntegrationDefinition_ReturnsNone() {
        // Arrange
        String ruleKey = "test-rule";
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenReturn(routingRules);

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, testMessage, stepConfig, null);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.NONE, result.getRouteType());
    }

    @Test
    void testEvaluate_ExceptionInRepository_ReturnsNone() {
        // Arrange
        String ruleKey = "test-rule";
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenThrow(new RuntimeException("Database error"));

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, testMessage, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.NONE, result.getRouteType());
    }

    @Test
    void testFindHandlerConfigRefById_Success() {
        // This tests the private method indirectly through the evaluate method
        String ruleKey = "test-rule";
        RoutingRule handlerRule = new RoutingRule();
        handlerRule.setRouteType(RoutingDecision.RouteType.HANDLER);
        handlerRule.setHandlerConfigRefId("handler-2");
        handlerRule.setCondition("true");
        
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenReturn(Collections.singletonList(handlerRule));

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, testMessage, stepConfig, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.HANDLER, result.getRouteType());
        assertNotNull(result.getHandlerConfigRef());
        assertEquals("handler-2", result.getHandlerConfigRef().getId());
        assertEquals("kafka-handler", result.getHandlerConfigRef().getType());
    }

    @Test
    void testEvaluate_MultipleRules_ReturnsFirstMatch() {
        // Arrange
        String ruleKey = "test-rule";
        when(hipIntegrationRegistry.findRoutingRulesByKey(ruleKey)).thenReturn(routingRules);

        // Act
        RoutingDecision result = routingRuleEngine.evaluate(ruleKey, testMessage, stepConfig, integrationDefinition);

        // Assert - Should return the first rule (channel rule)
        assertNotNull(result);
        assertEquals(RoutingDecision.RouteType.CHANNEL, result.getRouteType());
        assertEquals("test-channel", result.getChannelName());
    }
}
