package com.dell.it.hip.strategy.adapters;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

@Configuration
public class DynamicHttpRouterConfig {

    @Autowired
    private DynamicHttpsInputAdapter dynamicHttpsInputAdapter;

    @Bean
    public RouterFunction<ServerResponse> dynamicHttpRoutes() {
        RouterFunctions.Builder builder = RouterFunctions.route();

        dynamicHttpsInputAdapter.getHandlerMap().forEach((adapterKey, handler) -> {
            String path = "/hip/https/" + adapterKey;
            builder.POST(path, handler);
        });
        return builder.build();
    }
}