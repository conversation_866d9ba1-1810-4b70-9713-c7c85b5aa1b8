package com.dell.it.hip.config.adapters;


import java.util.List;

import org.springframework.stereotype.Component;
@Component("https")
public class DynamicHttpsAdapterConfig extends AdapterConfig {
    // --- Authentication ---
    private String apiKeyHeader;
    private String apiKeyValue;
    private boolean oAuthRequired;
    private String oAuthIssuerUrl; // For real token validation

    // --- Header Extraction ---
    private List<String> headersToExtract;

    // --- Performance ---
    private Integer maxRequestSizeBytes;
    private Integer maxConcurrency;
    private Integer requestTimeoutMs; // per request, ms
    private Integer rateLimitPerSecond; // soft throttle

    // --- Resiliency ---
    private Integer maxRetries;
    private Integer retryBackoffMs;
    private Boolean circuitBreakerEnabled;
    private Integer circuitBreakerThreshold;
    private Integer circuitBreakerResetMs;
    private Integer responseTimeoutMs;

    // --- CORS ---
    private List<String> allowedOrigins;
    private List<String> allowedMethods;
    private List<String> allowedHeaders;

    // --- Security ---
    private Boolean tlsClientCertRequired;
    private List<String> allowedCipherSuites;

    // --- HTTP ---
    private List<String> allowedHttpMethods;

    // === Getters/Setters ===
    // (Generate for each field below, or use Lombok if preferred)
    public String getApiKeyHeader() { return apiKeyHeader; }
    public void setApiKeyHeader(String apiKeyHeader) { this.apiKeyHeader = apiKeyHeader; }
    public String getApiKeyValue() { return apiKeyValue; }
    public void setApiKeyValue(String apiKeyValue) { this.apiKeyValue = apiKeyValue; }
    public boolean isOAuthRequired() { return oAuthRequired; }
    public void setOAuthRequired(boolean oAuthRequired) { this.oAuthRequired = oAuthRequired; }
    public String getOAuthIssuerUrl() { return oAuthIssuerUrl; }
    public void setOAuthIssuerUrl(String oAuthIssuerUrl) { this.oAuthIssuerUrl = oAuthIssuerUrl; }
    public List<String> getHeadersToExtract() { return headersToExtract; }
    public void setHeadersToExtract(List<String> headersToExtract) { this.headersToExtract = headersToExtract; }
    public Integer getMaxRequestSizeBytes() { return maxRequestSizeBytes; }
    public void setMaxRequestSizeBytes(Integer maxRequestSizeBytes) { this.maxRequestSizeBytes = maxRequestSizeBytes; }
    public Integer getMaxConcurrency() { return maxConcurrency; }
    public void setMaxConcurrency(Integer maxConcurrency) { this.maxConcurrency = maxConcurrency; }
    public Integer getRequestTimeoutMs() { return requestTimeoutMs; }
    public void setRequestTimeoutMs(Integer requestTimeoutMs) { this.requestTimeoutMs = requestTimeoutMs; }
    public Integer getRateLimitPerSecond() { return rateLimitPerSecond; }
    public void setRateLimitPerSecond(Integer rateLimitPerSecond) { this.rateLimitPerSecond = rateLimitPerSecond; }
    public Integer getMaxRetries() { return maxRetries; }
    public void setMaxRetries(Integer maxRetries) { this.maxRetries = maxRetries; }
    public Integer getRetryBackoffMs() { return retryBackoffMs; }
    public void setRetryBackoffMs(Integer retryBackoffMs) { this.retryBackoffMs = retryBackoffMs; }
    public Boolean getCircuitBreakerEnabled() { return circuitBreakerEnabled; }
    public void setCircuitBreakerEnabled(Boolean circuitBreakerEnabled) { this.circuitBreakerEnabled = circuitBreakerEnabled; }
    public Integer getCircuitBreakerThreshold() { return circuitBreakerThreshold; }
    public void setCircuitBreakerThreshold(Integer circuitBreakerThreshold) { this.circuitBreakerThreshold = circuitBreakerThreshold; }
    public Integer getCircuitBreakerResetMs() { return circuitBreakerResetMs; }
    public void setCircuitBreakerResetMs(Integer circuitBreakerResetMs) { this.circuitBreakerResetMs = circuitBreakerResetMs; }
    public Integer getResponseTimeoutMs() { return responseTimeoutMs; }
    public void setResponseTimeoutMs(Integer responseTimeoutMs) { this.responseTimeoutMs = responseTimeoutMs; }
    public List<String> getAllowedOrigins() { return allowedOrigins; }
    public void setAllowedOrigins(List<String> allowedOrigins) { this.allowedOrigins = allowedOrigins; }
    public List<String> getAllowedMethods() { return allowedMethods; }
    public void setAllowedMethods(List<String> allowedMethods) { this.allowedMethods = allowedMethods; }
    public List<String> getAllowedHeaders() { return allowedHeaders; }
    public void setAllowedHeaders(List<String> allowedHeaders) { this.allowedHeaders = allowedHeaders; }
    public Boolean getTlsClientCertRequired() { return tlsClientCertRequired; }
    public void setTlsClientCertRequired(Boolean tlsClientCertRequired) { this.tlsClientCertRequired = tlsClientCertRequired; }
    public List<String> getAllowedCipherSuites() { return allowedCipherSuites; }
    public void setAllowedCipherSuites(List<String> allowedCipherSuites) { this.allowedCipherSuites = allowedCipherSuites; }
    public List<String> getAllowedHttpMethods() { return allowedHttpMethods; }
    public void setAllowedHttpMethods(List<String> allowedHttpMethods) { this.allowedHttpMethods = allowedHttpMethods; }
}