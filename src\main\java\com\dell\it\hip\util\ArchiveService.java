package com.dell.it.hip.util;

import java.time.Instant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;

public class ArchiveService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveService.class);

    /**
     * Archives the message before/after output handling, as needed.
     */
    public void archive(Message<?> message,
                        HIPIntegrationDefinition def,
                        HandlerConfigRef ref) {
        try {
            String integrationName=def !=null ? def.getHipIntegrationName() :"unknown";
            String integrationVersion=def !=null ? def.getVersion() :"unknown";

            String businessflowName = def != null ? def.getBusinessFlowName(): "unknown";
            String handlerType = ref != null ? ref.getType() : "unknown";
            String configRef = ref != null ? ref.getId() : "unknown";
            String messageId = message != null && message.getHeaders().containsKey("id")
                    ? message.getHeaders().get("id").toString()
                    : null;

            // For now, just log; extend to write to S3, DB, NAS, etc. as needed.
            logger.info("[ARCHIVE] flowId={}, handlerType={}, configRef={}, msgId={}, timestamp={}, payload={}",
                    integrationName,integrationVersion,businessflowName, handlerType, configRef, messageId, Instant.now(), message != null ? message.getPayload() : null);

            // TODO: Actual file write or DB persistence here.

        } catch (Exception e) {
            logger.error("ArchiveService error: {}", e.getMessage(), e);
        }
    }
}
