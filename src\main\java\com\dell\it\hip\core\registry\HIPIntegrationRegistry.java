package com.dell.it.hip.core.registry;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.util.routing.RoutingRule;

@Component
public interface HIPIntegrationRegistry extends JpaRepository<HIPIntegrationRequestEntity, Long> {
    /**
     * Find all registered integrations for a given service manager (node, environment, or tenant).
     */
    List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName);

    /**
     * Find a single integration by manager, name, and version.
     */
    HIPIntegrationRequestEntity findByServiceManagerNameAndHipIntegrationNameAndVersion(
            String serviceManagerName, String hipIntegrationName, String version
    );

    /**
     * Delete a flow by manager, name, and version.
     */
    void deleteByServiceManagerNameAndHipIntegrationNameAndVersion(
            String serviceManagerName, String hipIntegrationName, String version
    );

    /**
     * Find routing rules by rule key.
     */
    List<RoutingRule> findRoutingRulesByKey(String ruleKey);

}