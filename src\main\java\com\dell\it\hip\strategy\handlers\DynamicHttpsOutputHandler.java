package com.dell.it.hip.strategy.handlers;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.DynamicHttpsHandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.util.ArchiveService;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.RetryTemplateFactory;
import com.dell.it.hip.util.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;

import reactor.netty.http.client.HttpClient;

@Component("https")
public class DynamicHttpsOutputHandler extends AbstractOutputHandlerStrategy {

    private static final Logger logger = LoggerFactory.getLogger(DynamicHttpsOutputHandler.class);

    @Autowired
    private OAuth2TokenService oauth2TokenService;

    @Autowired
    private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;

    @Autowired
    private TransactionLoggingUtil transactionLoggingUtil;

    @Autowired
    private WiretapService wiretapService;

    private final Map<String, AtomicBoolean> pausedMap = new HashMap<>();

    public DynamicHttpsOutputHandler(OpenTelemetryPropagationUtil otelUtil, WiretapService wiretapService, ArchiveService archiveService, RetryTemplateFactory retryTemplateFactory) {
        super(otelUtil, wiretapService, archiveService, retryTemplateFactory);
    }

    @Override
    public String getType() {
        return "https";
    }

    @Override
    public void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        DynamicHttpsHandlerConfig cfg = def.getConfig(ref.getPropertyRef(), DynamicHttpsHandlerConfig.class);
        if (cfg == null) throw new IllegalArgumentException("Invalid config for HTTPS handler: " + ref.getPropertyRef());
        String handlerKey = buildHandlerKey(def, ref);

        if (pausedMap.getOrDefault(handlerKey, new AtomicBoolean(false)).get()) {
            logger.warn("HTTPS OutputHandler paused: {}", handlerKey);
            wiretapService.tap(message, def, ref, "paused", "Output handler is paused");
            return;
        }

        Message<?> msgWithTrace = openTelemetryPropagationUtil.injectTraceContext(message);

        int attempts = cfg.getRetryAttempts() != null ? cfg.getRetryAttempts() : 1;
        long backoffMs = cfg.getRetryBackoffMs() != null ? cfg.getRetryBackoffMs() : 1000L;
        boolean success = false;
        Exception error = null;
        for (int i = 0; i < attempts; i++) {
            try {
                String responseBody = sendHttp(msgWithTrace, def, ref, cfg);
                wiretapService.tap(msgWithTrace, def, ref, "completed", "HTTPS output sent successfully, status: OK");
                transactionLoggingUtil.sendCompleted(msgWithTrace, def, "HTTPS output sent: " + responseBody);
                success = true;
                break;
            } catch (Exception ex) {
                logger.warn("HTTPS Output attempt {} failed: {}", i + 1, ex.getMessage());
                error = ex;
                if (i < attempts - 1) Thread.sleep(backoffMs);
            }
        }
        if (!success) {
            wiretapService.tap(msgWithTrace, def, ref, "error", "HTTPS output failed: " + (error != null ? error.getMessage() : "Unknown error"));
            transactionLoggingUtil.sendCompleted(msgWithTrace, def, "HTTPS output failed: " + (error != null ? error.getMessage() : "Unknown error"));
            throw error != null ? error : new RuntimeException("HTTPS Output failed");
        }
    }

    private String sendHttp(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref, DynamicHttpsHandlerConfig cfg) {
        // Set up client with optional connect timeout
        HttpClient httpClient = HttpClient.create();
        if (cfg.getConnectTimeoutMs() != null) {
            httpClient = httpClient.option(
                    io.netty.channel.ChannelOption.CONNECT_TIMEOUT_MILLIS, cfg.getConnectTimeoutMs().intValue());
        }

        WebClient.Builder builder = WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient));
        if (cfg.getMaxInMemorySize() != null) {
            builder.codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(cfg.getMaxInMemorySize()));
        }
        WebClient webClient = builder.build();

        // Prepare headers
        HttpHeaders headers = new HttpHeaders();
        if (cfg.getHeaders() != null) cfg.getHeaders().forEach(headers::add);
        if (cfg.getApiKeyHeader() != null && cfg.getApiKeyValue() != null) {
            headers.add(cfg.getApiKeyHeader(), cfg.getApiKeyValue());
        }
        if (cfg.getCompressed() != null && cfg.getCompressed()) {
            headers.add(HttpHeaders.CONTENT_ENCODING, "gzip");
        }
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        // OAuth2: Get token if enabled
        if (Boolean.TRUE.equals(cfg.isOauthEnabled())) {
            String accessToken = oauth2TokenService.getAccessToken(
                    cfg.getOauthTokenUrl(),
                    cfg.getOauthClientId(),
                    cfg.getOauthClientSecret(),
                    cfg.getOauthScope(),
                    cfg.getOauthAudience(),
                    cfg.getOauthAdditionalParams());
            if (accessToken != null) {
                headers.setBearerAuth(accessToken);
            }
        }

        // Prepare payload
        Object payload = message.getPayload();
        if (cfg.getCompressed() != null && cfg.getCompressed()) {
            payload = CompressionUtil.compress(payload);
        }

        WebClient.RequestHeadersSpec<?> requestSpec;
        WebClient.RequestBodySpec req = webClient
                .method(HttpMethod.valueOf(cfg.getHttpMethod() != null ? cfg.getHttpMethod() : "POST"))
                .uri(cfg.getEndpointUrl())
                .headers(h -> h.addAll(headers));

        if (!"GET".equalsIgnoreCase(cfg.getHttpMethod())) {
            requestSpec = req.body(BodyInserters.fromValue(payload));
        } else {
            requestSpec = req;
        }

        return requestSpec
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(cfg.getReadTimeoutMs() != null ? cfg.getReadTimeoutMs() : 10000))
                .block();
    }

    // -- Pause/Resume/Shutdown support (if needed) --
    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        pausedMap.computeIfAbsent(buildHandlerKey(def, ref), k -> new AtomicBoolean(false)).set(true);
        logger.info("HTTPS OutputHandler paused: {}", buildHandlerKey(def, ref));
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        pausedMap.computeIfAbsent(buildHandlerKey(def, ref), k -> new AtomicBoolean(false)).set(false);
        logger.info("HTTPS OutputHandler resumed: {}", buildHandlerKey(def, ref));
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        return pausedMap.getOrDefault(buildHandlerKey(def, ref), new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        pausedMap.remove(buildHandlerKey(def, ref));
        logger.info("HTTPS OutputHandler shutdown: {}", buildHandlerKey(def, ref));
    }
}