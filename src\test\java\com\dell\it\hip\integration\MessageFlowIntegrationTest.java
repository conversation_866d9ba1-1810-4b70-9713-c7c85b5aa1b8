package com.dell.it.hip.integration;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.ServiceManager;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * End-to-end integration tests for message flows using TestContainers.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@Testcontainers
class MessageFlowIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(DockerImageName.parse("postgres:15-alpine"))
            .withDatabaseName("hip_test")
            .withUsername("test")
            .withPassword("test")
            .withStartupTimeout(Duration.ofMinutes(2));

    @Container
    static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:6.2.6"))
            .withExposedPorts(6379)
            .withStartupTimeout(Duration.ofMinutes(2));

    @Container
    static KafkaContainer kafka = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
            .withStartupTimeout(Duration.ofMinutes(2));

    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockBean
    private ServiceManager serviceManager;

    @MockBean
    private HIPIntegrationOrchestrationService orchestrationService;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // PostgreSQL configuration
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);

        // Redis configuration
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", redis::getFirstMappedPort);

        // Kafka configuration
        registry.add("spring.kafka.bootstrap-servers", kafka::getBootstrapServers);
    }

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testHttpInputToKafkaOutput_SuccessFlow() throws Exception {
        // Arrange
        HIPIntegrationDefinition testIntegration = createTestIntegration();
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testIntegration);
        // Mock the processHIPIntegrationMessage method (it returns void)
        // We'll verify it was called instead of mocking a return value

        Map<String, Object> requestPayload = new HashMap<>();
        requestPayload.put("orderId", "12345");
        requestPayload.put("customerName", "John Doe");
        requestPayload.put("amount", 100.50);

        // Act & Assert
        mockMvc.perform(post("/hip/integrations/test-integration/1.0/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestPayload)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("success"));
    }

    @Test
    void testHttpInputWithValidation_FailureFlow() throws Exception {
        // Arrange - Send invalid payload
        Map<String, Object> invalidPayload = new HashMap<>();
        invalidPayload.put("invalidField", "value");

        // Act & Assert
        mockMvc.perform(post("/hip/integrations/test-integration/1.0/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidPayload)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.errorCode").exists())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    void testThrottlingBehavior() throws Exception {
        // Arrange
        HIPIntegrationDefinition testIntegration = createTestIntegration();
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testIntegration);

        // Simulate throttle limit exceeded
        doThrow(new RuntimeException("Throttle limit exceeded"))
                .when(orchestrationService).processHIPIntegrationMessage(eq("test-integration"), eq("1.0"), any());

        Map<String, Object> requestPayload = new HashMap<>();
        requestPayload.put("orderId", "12345");

        // Act & Assert
        mockMvc.perform(post("/hip/integrations/test-integration/1.0/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestPayload)))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.errorCode").value("INTERNAL_SERVER_ERROR"))
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    void testIntegrationNotFound() throws Exception {
        // Arrange
        when(serviceManager.getIntegrationDefinition("non-existent", "1.0"))
                .thenReturn(null);

        Map<String, Object> requestPayload = new HashMap<>();
        requestPayload.put("test", "data");

        // Act & Assert
        mockMvc.perform(post("/hip/integrations/non-existent/1.0/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestPayload)))
                .andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_NOT_FOUND"));
    }

    @Test
    void testHealthEndpoints() throws Exception {
        // Test that health endpoints are accessible
        mockMvc.perform(get("/actuator/health"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    void testMetricsEndpoint() throws Exception {
        // Test that metrics endpoint is accessible
        mockMvc.perform(get("/actuator/metrics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.names").isArray());
    }

    @Test
    void testConcurrentRequests() throws Exception {
        // Test concurrent request handling
        HIPIntegrationDefinition testIntegration = createTestIntegration();
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testIntegration);
        // Mock the processHIPIntegrationMessage method (it returns void)
        // We'll verify it was called instead of mocking a return value

        Map<String, Object> requestPayload = new HashMap<>();
        requestPayload.put("orderId", "12345");

        // Simulate multiple concurrent requests
        for (int i = 0; i < 5; i++) {
            mockMvc.perform(post("/hip/integrations/test-integration/1.0/process")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(requestPayload)))
                    .andExpect(status().isOk());
        }
    }

    @Test
    void testErrorRecovery() throws Exception {
        // Test error recovery mechanisms
        HIPIntegrationDefinition testIntegration = createTestIntegration();
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testIntegration);

        // First call fails, second succeeds
        doThrow(new RuntimeException("Temporary failure"))
                .doNothing()
                .when(orchestrationService).processHIPIntegrationMessage(eq("test-integration"), eq("1.0"), any());

        Map<String, Object> requestPayload = new HashMap<>();
        requestPayload.put("orderId", "12345");

        // First request should fail
        mockMvc.perform(post("/hip/integrations/test-integration/1.0/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestPayload)))
                .andExpect(status().isInternalServerError());

        // Second request should succeed (simulating recovery)
        mockMvc.perform(post("/hip/integrations/test-integration/1.0/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestPayload)))
                .andExpect(status().isOk());
    }

    private HIPIntegrationDefinition createTestIntegration() {
        HIPIntegrationDefinition integration = new HIPIntegrationDefinition();
        integration.setHipIntegrationName("test-integration");
        integration.setVersion("1.0");
        integration.setServiceManagerName("test-service-manager");
        integration.setBusinessFlowName("test-flow");
        integration.setDescription("Test integration for end-to-end testing");
        return integration;
    }
}
