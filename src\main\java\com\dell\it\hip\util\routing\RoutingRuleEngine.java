package com.dell.it.hip.util.routing;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.core.registry.HIPIntegrationRegistry;

/**
 * RuleEngine: Evaluates routing rules stored in database and returns a RoutingDecision.
 */
@Component
public class RoutingRuleEngine {

    private static final Logger logger = LoggerFactory.getLogger(RoutingRuleEngine.class);

    @Autowired
    private HIPIntegrationRegistry hipIntegrationRegistry;

    public RoutingDecision evaluate(String ruleKey, Message<?> message, FlowStepConfig stepConfig, HIPIntegrationDefinition def) {
        // Validate inputs
        if (ruleKey == null || ruleKey.trim().isEmpty()) {
            logger.debug("Rule key is null or empty, returning NONE routing decision");
            return RoutingDecision.none();
        }

        if (message == null || def == null) {
            logger.debug("Message or integration definition is null, returning NONE routing decision");
            return RoutingDecision.none();
        }

        try {
            // Step 1: Load rules from database
            List<RoutingRule> rules = hipIntegrationRegistry.findRoutingRulesByKey(ruleKey);
            if (rules == null || rules.isEmpty()) {
                logger.debug("No routing rules found for key: {}", ruleKey);
                return RoutingDecision.none();
            }

            // Step 2: Evaluate the first matching rule
            for (RoutingRule rule : rules) {
                if (rule.getRouteType() == RoutingDecision.RouteType.CHANNEL) {
                    if (evaluateCondition(rule.getCondition(), message, stepConfig, def)) {
                        logger.debug("Routing to channel: {}", rule.getChannelName());
                        return RoutingDecision.forChannel(rule.getChannelName());
                    }
                } else if (rule.getRouteType() == RoutingDecision.RouteType.HANDLER) {
                    if (evaluateCondition(rule.getCondition(), message, stepConfig, def)) {
                        HandlerConfigRef handlerRef = findHandlerConfigRefById(def, rule.getHandlerConfigRefId());
                        if (handlerRef != null) {
                            logger.debug("Routing to handler: {}", rule.getHandlerConfigRefId());
                            return RoutingDecision.forHandler(handlerRef);
                        } else {
                            logger.warn("Handler config ref not found: {}", rule.getHandlerConfigRefId());
                        }
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("Error evaluating routing rules for key: {}", ruleKey, ex);
        }

        logger.debug("No matching routing rules found for key: {}, returning NONE", ruleKey);
        return RoutingDecision.none();
    }

    /**
     * Evaluate the rule's condition based on message/step/def.
     * For now, always returns true. Can be extended to support SpEL or other condition languages.
     */
    private boolean evaluateCondition(String condition, Message<?> message, FlowStepConfig stepConfig, HIPIntegrationDefinition def) {
        // Simple condition evaluation - can be extended to support SpEL, regex, etc.
        if (condition == null || condition.trim().isEmpty() || "true".equalsIgnoreCase(condition.trim())) {
            return true;
        }
        if ("false".equalsIgnoreCase(condition.trim())) {
            return false;
        }
        // For more complex conditions, implement SpEL evaluation or other logic here
        logger.debug("Unknown condition '{}', defaulting to true", condition);
        return true;
    }

    /**
     * Find a HandlerConfigRef by its ID within the given integration definition
     */
    private HandlerConfigRef findHandlerConfigRefById(HIPIntegrationDefinition def, String handlerConfigRefId) {
        if (def == null || handlerConfigRefId == null) {
            return null;
        }

        return def.getHandlerConfigRefs().stream()
                .filter(ref -> handlerConfigRefId.equals(ref.getId()))
                .findFirst()
                .orElse(null);
    }
}